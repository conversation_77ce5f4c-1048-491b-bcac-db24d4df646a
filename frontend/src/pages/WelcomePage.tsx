import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../contexts/userContext';
import { motion, AnimatePresence } from 'framer-motion';
import {
    ArrowRight,
    Bot,
    Target,
    BrainCircuit,
    Sparkles,
    BarChart3,
    ClipboardCheck,
    ChevronLeft,
    ChevronRight,
    Zap,
    BookOpen,
    Users,
    TrendingUp,
    Shield,
    Lightbulb
} from 'lucide-react';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import ThemeToggle from '@/components/ThemeToggle';
import { usePageRefresh } from '@/hooks/usePageRefresh';

const WelcomePage: React.FC = () => {
    const navigate = useNavigate();
    const { user } = useUser();
    const [currentPage, setCurrentPage] = useState(0);
    const [autoPlay, setAutoPlay] = useState(true);
    usePageRefresh();

    const handleGetStarted = () => {
        navigate('/aegis-grader');
    };

    // Auto-advance pages
    useEffect(() => {
        if (!autoPlay) return;

        const interval = setInterval(() => {
            setCurrentPage((prev) => (prev + 1) % 3);
        }, 4000);

        return () => clearInterval(interval);
    }, [autoPlay]);

    const aegisGraderPages = [
        {
            title: "Smart Test Grading",
            subtitle: "AI-Powered Assessment",
            features: [
                {
                    icon: <Sparkles className="h-16 w-16 text-primary" />,
                    title: 'AI-Powered Grading',
                    description: 'Automatically grade handwritten tests with advanced AI that understands context and provides detailed feedback.',
                },
                {
                    icon: <ClipboardCheck className="h-16 w-16 text-primary" />,
                    title: 'Instant Results',
                    description: 'Get comprehensive grading results in minutes, not hours, with detailed breakdowns for each question.',
                },
            ],
            gradient: "from-primary/5 via-accent/5 to-secondary/5"
        },
        {
            title: "Effortless Workflow",
            subtitle: "Scan, Upload, Grade",
            features: [
                {
                    icon: <DocumentArrowUpIcon className="h-16 w-16 text-primary" />,
                    title: 'Multiple Input Methods',
                    description: 'Upload PDFs, scan documents with your camera, or drag and drop files for seamless test processing.',
                },
                {
                    icon: <BarChart3 className="h-16 w-16 text-primary" />,
                    title: 'Detailed Analytics',
                    description: 'Track class performance with comprehensive analytics and identify areas where students need support.',
                },
            ],
            gradient: "from-success/5 via-success/5 to-accent/5"
        },
        {
            title: "Professional Results",
            subtitle: "Export & Share",
            features: [
                {
                    icon: <DocumentTextIcon className="h-16 w-16 text-primary" />,
                    title: 'Professional Reports',
                    description: 'Generate detailed PDF reports with grades, feedback, and analytics ready for distribution.',
                },
                {
                    icon: <Users className="h-16 w-16 text-primary" />,
                    title: 'Easy Distribution',
                    description: 'Share results with students and parents through secure, professional-looking grade reports.',
                },
            ],
            gradient: "from-warning/5 via-destructive/5 to-primary/5"
        }
    ];



    const pages = aegisGraderPages;

    const nextPage = () => {
        setAutoPlay(false);
        setCurrentPage((prev) => (prev + 1) % 3);
    };

    const prevPage = () => {
        setAutoPlay(false);
        setCurrentPage((prev) => (prev - 1 + 3) % 3);
    };

    const goToPage = (index: number) => {
        setAutoPlay(false);
        setCurrentPage(index);
    };

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { y: 30, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                type: 'spring',
                stiffness: 100,
                damping: 15,
            },
        },
    };

    const pageVariants = {
        enter: (direction: number) => ({
            x: direction > 0 ? 1000 : -1000,
            opacity: 0,
        }),
        center: {
            zIndex: 1,
            x: 0,
            opacity: 1,
        },
        exit: (direction: number) => ({
            zIndex: 0,
            x: direction < 0 ? 1000 : -1000,
            opacity: 0,
        }),
    };

    const pageTransition = {
        type: 'tween',
        ease: 'anticipate',
        duration: 0.5,
    };

    return (
        <div className="h-screen bg-gradient-to-br from-background via-secondary/5 to-background flex items-center justify-center overflow-hidden">
            <div className="absolute top-4 right-4 z-50">
                <ThemeToggle size="default" />
            </div>

            {/* Navigation Arrows */}
            <button
                onClick={prevPage}
                className="absolute left-4 top-1/2 -translate-y-1/2 z-40 p-3 rounded-full bg-card/80 backdrop-blur-sm border border-border/50 hover:bg-card shadow-lg"
            >
                <ChevronLeft className="h-6 w-6 text-primary" />
            </button>

            <button
                onClick={nextPage}
                className="absolute right-4 top-1/2 -translate-y-1/2 z-40 p-3 rounded-full bg-card/80 backdrop-blur-sm border border-border/50 hover:bg-card shadow-lg"
            >
                <ChevronRight className="h-6 w-6 text-primary" />
            </button>

            <div className="layout-container flex h-full grow flex-col relative">
                <div className="px-4 md:px-20 lg:px-40 flex flex-1 justify-center py-5">
                    <div className="layout-content-container flex flex-col w-full max-w-6xl py-5 relative">
                        {/* Header Section */}
                        <motion.div
                            variants={containerVariants}
                            initial="hidden"
                            animate="visible"
                            className="flex flex-col items-center mb-8 z-10"
                        >
                            <motion.div variants={itemVariants} className="flex justify-center mb-6">
                                <AegisScholarLogoWithoutText
                                    className="w-16 h-16 text-accent"
                                    style={{ fill: 'var(--color-accent)' }}
                                />
                            </motion.div>

                            <motion.h2
                                variants={itemVariants}
                                className="text-primary tracking-tight text-3xl md:text-4xl font-bold leading-tight text-center pb-3 pt-2 font-['Space_Grotesk']"
                            >
                                Welcome to AegisGrader, {user?.firstName || user?.username}!
                            </motion.h2>

                            <motion.p
                                variants={itemVariants}
                                className="text-muted-foreground text-base md:text-lg font-normal leading-relaxed pb-4 pt-1 text-center max-w-xl mx-auto"
                            >
                                Discover the power of AI-driven test grading that transforms how you assess student work.
                            </motion.p>
                        </motion.div>

                        {/* Page Content */}
                        <div className="flex-1 relative">
                            <AnimatePresence mode="wait" custom={currentPage}>
                                <motion.div
                                    key={currentPage}
                                    custom={currentPage}
                                    variants={pageVariants}
                                    initial="enter"
                                    animate="center"
                                    exit="exit"
                                    transition={pageTransition}
                                    className="absolute inset-0 flex flex-col"
                                >
                                    {/* Page Background */}
                                    <div className={`absolute inset-0 bg-gradient-to-br ${pages[currentPage].gradient} rounded-3xl opacity-50`} />

                                    {/* Page Content */}
                                    <div className="relative z-10 flex flex-col h-full p-8">
                                        <div className="text-center mb-12">
                                            <h3 className="text-2xl md:text-3xl font-bold text-primary mb-2 font-['Space_Grotesk']">
                                                {pages[currentPage].title}
                                            </h3>
                                            <p className="text-lg text-muted-foreground font-medium">
                                                {pages[currentPage].subtitle}
                                            </p>
                                        </div>

                                        <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                                            {pages[currentPage].features.map((feature, index) => (
                                                <motion.div
                                                    key={index}
                                                    initial={{ opacity: 0, y: 50 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    transition={{ delay: index * 0.2 }}
                                                    className="flex flex-col items-center text-center p-6 bg-card/60 backdrop-blur-sm rounded-2xl border border-border/30"
                                                >
                                                    <div className="mb-6 p-4 rounded-2xl">
                                                        {feature.icon}
                                                    </div>
                                                    <h4 className="text-xl font-bold text-primary mb-4 font-['Space_Grotesk']">
                                                        {feature.title}
                                                    </h4>
                                                    <p className="text-muted-foreground leading-relaxed">
                                                        {feature.description}
                                                    </p>
                                                </motion.div>
                                            ))}
                                        </div>
                                    </div>
                                </motion.div>
                            </AnimatePresence>
                        </div>

                        {/* Pagination Dots */}
                        <div className="flex justify-center items-center space-x-3 mt-8 mb-6 z-10">
                            {[0, 1, 2].map((index) => (
                                <button
                                    key={index}
                                    onClick={() => goToPage(index)}
                                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                                        index === currentPage
                                            ? 'bg-accent w-8'
                                            : 'bg-muted-foreground/30 hover:bg-muted-foreground/60'
                                    }`}
                                />
                            ))}
                        </div>

                        {/* CTA Button */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.5 }}
                            className="flex justify-center z-10"
                        >
                            <motion.button
                                onClick={handleGetStarted}
                                className="flex min-w-[200px] max-w-[400px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-14 px-8 bg-accent hover:bg-accent/90 text-accent-foreground text-base font-bold leading-normal tracking-wide shadow-lg"
                            >
                                <span className="truncate mr-2">Let's Get Started</span>
                                <ArrowRight className="h-5 w-5" />
                            </motion.button>
                        </motion.div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WelcomePage;