import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline';
import GradingSubmissions from '@/components/GradingSubmissions';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import ThemeToggle from '@/components/ThemeToggle';
import { useUser } from '../contexts/userContext';

const GradingSubmissionsPage: React.FC = () => {
    const navigate = useNavigate();
    const { user, setUser } = useUser();

    return (
        <div className="min-h-screen w-full bg-background p-3 pb-16">
            <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6 lg:space-y-8">
                {/* Header */}
                <header className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-3">
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                        {/* <button
                            onClick={() => navigate(-1)}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-colors min-h-[44px] touch-manipulation"
                            title="Back to AegisGrader"
                        >
                            <ArrowLeftIcon className="w-4 h-4" />
                            <span className="hidden xs:inline sm:inline">Back</span>
                        </button> */}
                        <div className="min-w-0 flex-1">
                            <h1 className="text-lg sm:text-xl lg:text-2xl font-['Space_Grotesk'] font-bold text-foreground truncate">Grading Submissions</h1>
                            <p className="text-xs sm:text-sm text-muted-foreground line-clamp-1">
                                View and manage your test grading submissions
                            </p>
                        </div>
                    </div>
                </header>

                {/* Main Content */}
                <div className="space-y-4 sm:space-y-6">
                    <GradingSubmissions submissions={[]} />
                </div>
            </div>
        </div>
    );
};

export default GradingSubmissionsPage;
